from enum import Enum
from pathlib import Path
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict

from .base_llm_config import SimilarityConfig


class APIProvider(str, Enum):
    """API providers for LLM services."""

    OPENAI = "openai"
    GOOGLE = "google"
    ANTHROPIC = "anthropic"

    @classmethod
    def get_all_providers(cls) -> list[str]:
        """Get all available provider values."""
        return [provider.value for provider in cls]

    @classmethod
    def is_valid_provider(cls, provider: str) -> bool:
        """Check if a provider string is valid."""
        return provider in cls.get_all_providers()


class APIKeyManager(BaseModel):
    """Centralized API key management."""

    # Environment-based API keys
    api_keys: Dict[str, Optional[str]] = Field(default_factory=dict)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Initialize with empty dict if not provided
        if not self.api_keys:
            self.api_keys = {}

    def set_api_key(self, provider: APIProvider, key: Optional[str]) -> None:
        """Set API key for a provider."""
        self.api_keys[provider.value] = key

    def get_api_key(self, provider: APIProvider) -> Optional[str]:
        """Get API key for specified provider."""
        return self.api_keys.get(provider.value)

    def has_api_key(self, provider: APIProvider) -> bool:
        """Check if API key exists for provider."""
        key = self.get_api_key(provider)
        return key is not None and key.strip() != ""


class LLMProviderConfig(BaseModel):
    """Configuration for a specific LLM provider."""

    provider: APIProvider
    default_model: str
    available_models: list[str] = Field(default_factory=list)
    max_tokens_limit: int = Field(default=4096)
    supports_streaming: bool = Field(default=True)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Add default model to available models if not present
        if self.default_model not in self.available_models:
            self.available_models.insert(0, self.default_model)


class LLMSettings(BaseModel):
    """Centralized LLM configuration with provider management."""

    # API Key Manager
    api_key_manager: APIKeyManager = Field(default_factory=APIKeyManager)

    # Provider Configurations
    providers: Dict[str, LLMProviderConfig] = Field(default_factory=dict)

    # Default provider
    default_provider: APIProvider = Field(default=APIProvider.GOOGLE)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._initialize_default_providers()

    def _initialize_default_providers(self) -> None:
        """Initialize default provider configurations."""
        if not self.providers:
            self.providers = {
                APIProvider.GOOGLE.value: LLMProviderConfig(
                    provider=APIProvider.GOOGLE,
                    default_model="gemini-2.5-flash",
                    available_models=[
                        "gemini-2.5-flash",
                    ],
                    max_tokens_limit=8192,
                    supports_streaming=True,
                ),
                APIProvider.OPENAI.value: LLMProviderConfig(
                    provider=APIProvider.OPENAI,
                    default_model="gpt-4.1-mini",
                    available_models=["gpt-4.1-mini"],
                    max_tokens_limit=4096,
                    supports_streaming=True,
                ),
                APIProvider.ANTHROPIC.value: LLMProviderConfig(
                    provider=APIProvider.ANTHROPIC,
                    default_model="claude-3-sonnet-20240229",
                    available_models=[
                        "claude-3-sonnet-20240229",
                        "claude-3-haiku-20240307",
                    ],
                    max_tokens_limit=4096,
                    supports_streaming=True,
                ),
            }

    def get_api_key(self, provider: APIProvider) -> Optional[str]:
        """Get API key for specified provider."""
        return self.api_key_manager.get_api_key(provider)

    def get_provider_config(self, provider: APIProvider) -> Optional[LLMProviderConfig]:
        """Get provider configuration."""
        return self.providers.get(provider.value)

    def get_default_model(self, provider: APIProvider) -> str:
        """Get default model for specified provider."""
        config = self.get_provider_config(provider)
        return config.default_model if config else "gemini-2.5-flash"

    def get_available_providers(self) -> list[APIProvider]:
        """Get list of providers with valid API keys."""
        return [
            APIProvider(provider_name)
            for provider_name in self.providers.keys()
            if self.api_key_manager.has_api_key(APIProvider(provider_name))
        ]

    def is_provider_available(self, provider: APIProvider) -> bool:
        """Check if provider is available (has API key and configuration)."""
        return provider.value in self.providers and self.api_key_manager.has_api_key(
            provider
        )


class RAGSearchSettings(SimilarityConfig):
    """Configuration for RAG similarity search operations."""

    max_candidates_per_index: int = Field(default=5, ge=1, le=20)
    search_timeout_seconds: float = Field(default=30.0, ge=1.0)
    enable_fallback_search: bool = Field(default=True)


class Settings(BaseSettings):
    """應用程式設定 - 重構後的統一配置管理"""

    # 專案根目錄（基於當前工作目錄）
    project_root: Path = Field(default_factory=lambda: Path.cwd())

    # 環境設定
    environment: str = Field("local", env="ENVIRONMENT")

    # 環境變數 API 密鑰（僅用於從環境讀取）
    gemini_api_key: str = Field(..., env="GEMINI_API_KEY")
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")

    # 集中的LLM設定
    llm: LLMSettings = Field(default_factory=LLMSettings)

    # Elasticsearch設定
    elasticsearch_url: str = Field("http://127.0.0.1:9200", env="ELASTICSEARCH_URL")
    elasticsearch_username: Optional[str] = Field(None, env="ELASTICSEARCH_USERNAME")
    elasticsearch_password: Optional[str] = Field(None, env="ELASTICSEARCH_PASSWORD")

    # Index名稱
    main_categories_index: str = Field("main_categories", env="MAIN_CATEGORIES_INDEX")
    sub_categories_index: str = Field("sub_categories", env="SUB_CATEGORIES_INDEX")

    # Embedding設定
    embedding_dims: int = Field(3072, env="EMBEDDING_DIMS")
    embedding_task_type: str = Field("RETRIEVAL_DOCUMENT", env="EMBEDDING_TASK_TYPE")

    # RAG Search設定
    rag_search: RAGSearchSettings = Field(default_factory=RAGSearchSettings)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._initialize_llm_settings()

    def _initialize_llm_settings(self) -> None:
        """Initialize centralized LLM settings with API keys from environment."""
        # Set up API keys in the centralized manager
        self.llm.api_key_manager.set_api_key(APIProvider.GOOGLE, self.gemini_api_key)
        self.llm.api_key_manager.set_api_key(APIProvider.OPENAI, self.openai_api_key)
        self.llm.api_key_manager.set_api_key(
            APIProvider.ANTHROPIC, self.anthropic_api_key
        )

    def get_llm_config_for_service(self, service_name: str) -> Dict[str, Any]:
        """Get LLM configuration for a specific service."""
        base_config = {
            "api_key_manager": self.llm.api_key_manager,
            "providers": self.llm.providers,
            "default_provider": self.llm.default_provider,
        }

        # Service-specific overrides can be added here
        service_overrides = {
            "intent_detection": {
                "primary_provider": APIProvider.GOOGLE,
                "fallback_provider": APIProvider.OPENAI,
                "max_tokens": 4096,
            },
            "classification": {
                "primary_provider": APIProvider.GOOGLE,
                "max_tokens": 500,
            },
        }

        if service_name in service_overrides:
            base_config.update(service_overrides[service_name])

        return base_config

    model_config = SettingsConfigDict(
        extra="ignore",  # Ignore extra fields for backward compatibility
        case_sensitive=False,
    )
