"""Agno-based LLM Classification Service for complaint categorization."""

import logging
from datetime import datetime
from typing import List, Optional

from agno.agent import Agent
from agno.models.google import Gemini
from agno.models.openai import OpenAIChat
from pydantic import BaseModel, Field, validator

from src.config.classification_config import ClassificationConfig
from src.domain.enums.llm_provider import LLMProvider
from src.domain.interfaces.classification_service_interface import (
    ClassificationServiceInterface,
)

from ...domain.entities.classification_result import ClassificationResult
from ...domain.entities.confidence_level import ConfidenceLevel
from ...domain.entities.intent_type import IntentType
from ...domain.entities.rag_candidate import RAGCandidate

logger = logging.getLogger(__name__)


class ClassificationResponse(BaseModel):
    """Structured response model for LLM classification results."""

    category: str = Field(..., description="Selected sub-category name")
    main_category: str = Field(..., description="Corresponding main category name")
    confidence: str = Field(..., description="High or Low confidence level")
    reasoning: str = Field(
        ..., min_length=10, description="Detailed classification reasoning"
    )

    @validator("confidence")
    def validate_confidence_level(cls, v):
        if v not in ["high", "low"]:
            raise ValueError("Confidence must be either 'high' or 'low'")
        return v


class AgnoLLMClassificationService(ClassificationServiceInterface):
    """Agno-based implementation of LLM Classification Service with multi-provider support."""

    def __init__(self, config: ClassificationConfig):
        self.config = config
        self._classification_agent = None
        self._initialize_agent()

    def _initialize_agent(self):
        """Initialize Agno Agent with the configured LLM provider."""

        # Get API key for the selected provider
        api_key = self.config.get_api_key_for_provider()
        if not api_key:
            raise ValueError(f"API key not provided for {self.config.llm_provider}")

        # Configure model based on provider with temperature
        if self.config.llm_provider == LLMProvider.OPENAI:
            model = OpenAIChat(
                id="gpt-4.1-mini",
                api_key=api_key,
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens,
            )
        elif self.config.llm_provider == LLMProvider.GOOGLE:
            model = Gemini(
                id="gemini-2.5-flash",
                api_key=api_key,
                temperature=self.config.temperature,
            )
        else:
            raise ValueError(f"Unsupported model provider: {self.config.llm_provider}")

        # Initialize Agno Agent with structured output
        self._classification_agent = Agent(
            model=model,
            description="台灣政府陳情案件分類專家，專門根據案件內容和相似度結果進行精確分類",
            instructions=self._build_classification_instructions(),
            response_model=ClassificationResponse,
            structured_outputs=True,
            markdown=False,
            show_tool_calls=False,
            telemetry=False,
            monitoring=False,
        )

        logger.info(
            f"Initialized LLM Classification Agent with provider: {self.config.llm_provider}"
        )

    def _build_classification_instructions(self) -> str:
        """Build comprehensive instructions for the classification agent."""
        return """
你是一個台灣政府陳情案件分類專家。請根據提供的資訊進行分類：

分類原則：
1. 仔細分析陳情內容的核心訴求和意圖
2. 參考相似度搜尋結果選擇最適合的類別
3. 確保主類別與子類別的一致性
4. 如果對分類很有把握且相似度高，輸出 "high" 信心度
5. 如果有不確定性或相似度偏低，輸出 "low" 信心度
6. 如果沒有合適的類別匹配，分類到 "其它" 並說明原因

回應格式：
- category: 選定的子類別名稱
- main_category: 對應的主類別名稱
- confidence: "high" 或 "low"
- reasoning: 詳細的分類理由和信心度判斷依據
"""

    async def classify_with_context(
        self,
        complaint_input: str,
        rag_candidates: List[RAGCandidate],
        detected_intents: List[IntentType],
        summary: Optional[str] = None,
        case_id: Optional[str] = None,
    ) -> ClassificationResult:
        """
        Generate classification using Agno Agent with structured output.

        Args:
            complaint_input: Original complaint text
            rag_candidates: RAG similarity search results
            detected_intents: List of detected intents
            summary: Optional content summary
            case_id: Optional case identifier

        Returns:
            ClassificationResult with LLM-generated classification
        """
        start_time = datetime.now()

        try:
            # Build classification prompt
            prompt = self._build_classification_prompt(
                complaint_input, rag_candidates, detected_intents, summary
            )

            # Get structured response from Agno Agent
            logger.info(f"Sending classification request to {self.config.llm_provider}")
            response = await self._classification_agent.arun(prompt)
            response = response.content

            # Calculate processing time
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)

            # Extract highest similarity score from RAG candidates
            max_similarity = max(
                [c.similarity_score for c in rag_candidates], default=0.0
            )

            # Convert to our domain model
            result = ClassificationResult(
                case_id=case_id or "unknown",
                main_category=response.main_category,
                sub_category=response.category,
                sub_description=f"由 {self.config.llm_provider} 模型分類",
                intents=[intent.intent_name for intent in detected_intents],
                confidence=response.confidence,
                reasoning=response.reasoning,
                rag_candidates=rag_candidates,
                similarity_score=max_similarity,
                processing_time_ms=processing_time,
            )

            logger.info(
                f"Classification completed: {result.sub_category} "
                f"(confidence: {result.confidence}, time: {processing_time}ms)"
            )

            return result

        except Exception as e:
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            logger.error(f"Agno classification failed: {e}")
            return self._create_default_classification(
                complaint_input, str(e), case_id, processing_time
            )

    def _build_classification_prompt(
        self,
        complaint: str,
        rag_candidates: List[RAGCandidate],
        intents: List[IntentType],
        summary: Optional[str] = None,
    ) -> str:
        """Build structured prompt for classification."""

        # Build complaint content section
        content_parts = [f"原始陳情內容：{complaint}"]

        if intents:
            intent_names = [
                intent.value if hasattr(intent, "value") else str(intent)
                for intent in intents
            ]
            content_parts.append(f"偵測到的意圖：{', '.join(intent_names)}")

        if summary:
            content_parts.append(f"內容摘要：{summary}")

        complaint_section = "\n\n".join(content_parts)

        # Build RAG results section
        rag_section = "相似類別搜尋結果：\n"
        for idx, candidate in enumerate(rag_candidates[:5], 1):  # Limit to top 5
            rag_section += f"{idx}. 類別：{candidate.sub_category} (主類別：{candidate.main_category})\n"
            rag_section += f"   相似度：{candidate.similarity_score:.3f}\n"
            if hasattr(candidate, "description") and candidate.description:
                rag_section += f"   描述：{candidate.description}\n"
            rag_section += "\n"

        return f"""請根據以下資訊進行陳情案件分類：

## 陳情案件資訊
{complaint_section}

## {rag_section}

請提供分類結果："""

    def _create_default_classification(
        self,
        complaint: str,
        failure_reason: str,
        case_id: Optional[str] = None,
        processing_time: int = 0,
    ) -> ClassificationResult:
        """Create default classification when processing fails."""

        return ClassificationResult(
            case_id=case_id or "unknown",
            main_category="其它",
            sub_category="其它",
            sub_description="系統預設分類",
            intents=[],
            confidence=ConfidenceLevel.LOW,
            reasoning=f"分類失敗，預設分類至其它類別。失敗原因：{failure_reason}",
            rag_candidates=[],
            similarity_score=0.0,
            processing_time_ms=processing_time,
        )
