"""
Main application entry point for AI RAG Complaint Classification System.

This module demonstrates the complete AI-powered complaint classification system
with the following capabilities:

1. Clean Architecture implementation with separation of concerns
2. Intent detection and content summarization using Agno framework
3. RAG similarity search with Elasticsearch vector storage
4. Multi-LLM classification support (OpenAI, Anthropic, Google)
5. Batch processing with configurable concurrency
6. Comprehensive error handling and fallback mechanisms

Usage:
    uv run src/main.py

Environment Variables Required:
    GEMINI_API_KEY - Google Gemini API key
    OPENAI_API_KEY - OpenAI API key (optional)
    ANTHROPIC_API_KEY - Anthropic API key (optional)
    ELASTICSEARCH_URL - Elasticsearch server URL
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to Python path for imports
sys.path.append(str(Path(__file__).parent.parent))

from dotenv import load_dotenv

from src.application.use_cases.classify_complaints import ClassifyComplaintsUseCase
from src.application.use_cases.detect_intents import DetectIntentsUseCase
from src.application.use_cases.similarity_search import SimilaritySearchUseCase
from src.config import Settings, get_config_manager
from src.domain.entities.complaint_input import ComplaintInput
from src.domain.entities.confidence_level import ConfidenceLevel
from src.infrastructure.embedders.gemini_embedder import GeminiEmbedder
from src.infrastructure.llm_classification.agno_llm_classifier import (
    AgnoLLMClassificationService,
)
from src.infrastructure.monitoring.classification_metrics import ClassificationMetrics
from src.infrastructure.rag_elasticsearch_retriever import RAGElasticsearchRetriever
from src.infrastructure.repositories.elasticsearch_repository import (
    ElasticsearchRepository,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def setup_system(settings: Settings):
    """
    Set up the complete AI RAG system with all components.

    Returns:
        Tuple of (orchestrator, repository, embedder)
    """

    logger.info("🚀 Initializing AI RAG Complaint Classification System")

    # Initialize core infrastructure components
    logger.info("📦 Setting up infrastructure components...")

    # Embedder for processing categories (data ingestion)
    embedder = GeminiEmbedder(settings)

    # Elasticsearch repository for vector storage
    repository = ElasticsearchRepository(settings)

    # RAG retriever for similarity search
    rag_retriever = RAGElasticsearchRetriever(settings, embedder)

    # Initialize application use cases
    logger.info("🔧 Configuring application use cases...")

    # Get configuration manager for centralized config management
    config_manager = get_config_manager()

    # Intent detection use case with new config system
    intent_config = config_manager.get_intent_detection_config()
    detect_intents_use_case = DetectIntentsUseCase(intent_config)

    # Similarity search use case
    similarity_search_use_case = SimilaritySearchUseCase(rag_retriever, settings)

    # Initialize classification service and metrics with new config system
    classification_config = config_manager.get_classification_config()
    classification_service = AgnoLLMClassificationService(classification_config)
    metrics = ClassificationMetrics()

    # Classification orchestrator
    classification_orchestrator = ClassifyComplaintsUseCase(
        detect_intents_use_case=detect_intents_use_case,
        similarity_search_use_case=similarity_search_use_case,
        classification_service=classification_service,
        metrics=metrics,
        enable_llm_analysis=classification_config.enable_llm_analysis,
    )

    logger.info(
        f"✅ System initialized with LLM provider: {classification_config.llm_provider}"
    )

    return classification_orchestrator, repository, embedder


async def demo_classification_workflow(orchestrator: ClassifyComplaintsUseCase):
    """Demonstrate the complete classification workflow with sample data."""

    logger.info("\n" + "=" * 60)
    logger.info("🎯 CLASSIFICATION WORKFLOW DEMONSTRATION")
    logger.info("=" * 60)

    # Sample complaints for demonstration
    sample_complaints = [
        ComplaintInput(
            case_id="MAIN-001",
            subject="市民公車服務問題客訴",
            content="今天中午我媽媽於南門市場約12:30左右站欲搭乘225A公車回家，在上車時才將雨傘拐杖撐到車門階梯欲上車時突然間車門即關閉並開走，還好我八十一歲的母親機警放掉拐杖，否則就被公車拖走，你們這是謀殺吧！我媽媽事後就坐計程車追公車找到拐杖，我有拐杖受損照片佐證可以佐證司機的疏忽與惡劣行徑，呈請市長以慈悲胸懷多多體諒這些年長父母們行動不便，要求這些司機不應該以績效為主而是要以民眾安全為主，白天上班時間要搭乘的都是老弱婦孺居多，如果司機素質如此，則是民安全真的堪慮，希望我反應後市長大人能正視這問題不要等到發生意外了才要道歉，市民不要道歉，而是我們反映了要正視問題徹底解決減少意外的悲劇，最後謝謝市長百忙撥冗看信，還請市長多多幫忙這些老人家！誠心希望市長提供給大家的是一輛平安到家的公車而不是奪命公車謝謝！（附上今天被車門夾著跑的雨傘拐杖慘況照片，這如果夾的是我媽媽的腳，應該是一條人命了吧！）",
        ),
        # ComplaintInput(
        #     case_id="MAIN-002",
        #     subject="拖吊未留資訊，浪費民眾時間",
        #     content="臨停黃線幫忙家人處理事情回來卻不見機車蹤跡，一次性拖走近20台機車，卻一台拖吊資訊都未留下\r\n回程陪家中老人尋找機車時，足足浪費20-30分鐘時間尋找，導致上班遲到扣錢",
        # ),
        # ComplaintInput(
        #     case_id="MAIN-003",
        #     subject="大園埔心派出所拒絕民眾申請路口監視器",
        #     content="現場也有監視器，證明當時有發生二起車禍，我要調路口監視器，我有去大園派出所看監視器，警察不給我看，直接說那你去申訴！\r\n警察完全不通人情就直接開單，完全沒有看現場的狀況，如果今天沒有這兩起車禍青昇路一段會這麼塞嗎？\r\n警察處理交通事故是這樣處理的嗎？不是盡快處理車禍讓路口順暢，為何是站在馬路邊處理車禍，而讓車禍現場阻擋路口\r\n\r\n本人行經桃園大園青昇路一段時看到2起車禍，那時候有看到員警已經在現場處理，但是還是造成道路整個大塞爆，當下我行經第二車禍時，我開在內側車道看到前方有三角錐在車禍的路口，我要切外側切不出去，一直看後照鏡後方來車，避免與直行車發生碰撞，那時候交通號誌顯示綠燈，外側車道車輛很多，沒有車輛停止讓我外切，所以我一直無法出去，也沒有警察協助指揮交通，當我以為外側有車輛停止願意讓我切出去到路口時，已經變成紅燈，我當時眼睛要很專注於右車道，避免發生車禍，結果被警察用密錄器開我闖紅燈，我並非故意闖紅燈，而檢舉人是警察，畫面提供則是警察身上的密錄器，為什麼當下不攔車。\r\n《道路交通管理處罰條例》規定，汽車駕駛人闖紅燈，當場不能或不適合攔截舉發者，可以逕行舉發。但依《取締一般交通違規作業程序》，員警執行勤務發現交通違規，應攔停取締，除非民眾不服攔查，駕車逃逸，才能以密錄器畫面進行舉發。如果警察當下有趕快處理車禍，現場請車主移到路邊去處理就不會發生塞車的事情發生，三角錐擺放位置也沒有按照交通規定的30公尺以上，導致我們用路人開過去時，才發現路口有車禍，警察用密錄器檢舉我闖紅燈，實在是不合理的事。",
        # ),
    ]

    logger.info(f"📝 Processing {len(sample_complaints)} sample complaints...")

    try:
        # Single complaint demonstration
        logger.info("\n🔍 Single Complaint Classification:")
        result = await orchestrator.execute(sample_complaints[0])

        logger.info(f"   Case ID: {result.case_id}")
        logger.info(f"   Category: {result.main_category} > {result.sub_category}")
        logger.info(f"   Confidence: {result.confidence}")
        logger.info(f"   Similarity Score: {result.similarity_score:.3f}")
        logger.info(f"   Intents: {len(result.intents)} detected")
        logger.info(f"   Processing Time: {result.processing_time_ms}ms")

        # Batch processing demonstration
        logger.info(f"\n📦 Batch Processing ({len(sample_complaints)} complaints):")
        batch_results = await orchestrator.execute_batch(
            sample_complaints, concurrency_limit=2
        )

        # Summary statistics
        high_confidence = sum(
            1 for r in batch_results if r.confidence == ConfidenceLevel.HIGH
        )
        avg_processing_time = sum(
            r.processing_time_ms or 0 for r in batch_results
        ) / len(batch_results)

        logger.info(f"   Successfully processed: {len(batch_results)} complaints")
        logger.info(
            f"   High confidence results: {high_confidence}/{len(batch_results)} ({high_confidence / len(batch_results) * 100:.1f}%)"
        )
        logger.info(f"   Average processing time: {avg_processing_time:.0f}ms")

        # Performance metrics
        logger.info("\n📊 Performance Metrics:")
        metrics = orchestrator.get_performance_metrics()
        if metrics.get("status") != "no_data":
            logger.info(f"   Total processed: {metrics['total_processed']}")
            logger.info(f"   Success rate: {metrics['success_rate'] * 100:.1f}%")
            logger.info(f"   Average total time: {metrics['avg_total_time_ms']:.0f}ms")
            logger.info(
                f"   Average intent time: {metrics['avg_intent_time_ms']:.0f}ms"
            )
            logger.info(f"   Average RAG time: {metrics['avg_rag_time_ms']:.0f}ms")
            logger.info(f"   Average LLM time: {metrics['avg_llm_time_ms']:.0f}ms")

        return True

    except Exception as e:
        logger.error(f"❌ Classification demonstration failed: {e}")
        return False


async def demo_data_processing(
    repository: ElasticsearchRepository, embedder: GeminiEmbedder
):
    """Demonstrate data processing and ingestion (if needed)."""

    logger.info("\n" + "=" * 60)
    logger.info("📚 DATA PROCESSING DEMONSTRATION")
    logger.info("=" * 60)

    try:
        logger.info(
            "✅ Data processing demonstration - using existing repository setup"
        )
        logger.info("   Classification system is ready to process complaints")
        return True

    except Exception as e:
        logger.error(f"❌ Data processing check failed: {e}")
        return False


async def main():
    """Main application entry point."""

    print("\n🚀 AI RAG 台灣政府陳情分類系統")
    print("=" * 80)
    print("Clean Architecture + Multi-LLM + RAG Search + Intent Detection")
    print("=" * 80)

    try:
        # Load environment variables first
        load_dotenv()

        # Load configuration
        logger.info("⚙️  Loading system configuration...")
        settings = Settings()

        # Create config manager with our settings instance
        from src.config.config_factory import ConfigurationManager

        config_manager = ConfigurationManager(settings)
        classification_config = config_manager.get_classification_config()

        logger.info(f"   Environment: {settings.environment}")
        logger.info(f"   Elasticsearch: {settings.elasticsearch_url}")
        logger.info(f"   LLM Provider: {classification_config.llm_provider}")
        logger.info(
            f"   LLM Analysis (Intent + Summary): {'Enabled' if classification_config.enable_llm_analysis else 'Disabled'}"
        )

        # Set up system
        orchestrator, repository, embedder = await setup_system(settings)

        # Demonstrate data processing
        data_success = await demo_data_processing(repository, embedder)

        # Demonstrate classification workflow
        if data_success:
            classification_success = await demo_classification_workflow(orchestrator)

            if classification_success:
                logger.info("\n✨ System demonstration completed successfully!")
                logger.info(
                    "   For more detailed examples, run: uv run demo_classification_orchestrator.py"
                )
            else:
                logger.warning("⚠️  Classification demonstration had issues")
        else:
            logger.warning(
                "⚠️  Data processing check failed - classification may not work properly"
            )

        print("\n" + "=" * 80)
        print("🎉 Application execution completed!")
        print("=" * 80)

        return 0

    except Exception as e:
        logger.error(f"❌ Application failed: {e}")
        print(f"\n❌ Application failed: {e}")
        return 1


if __name__ == "__main__":
    # Run the application
    exit_code = asyncio.run(main())
    exit(exit_code)
